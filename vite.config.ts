/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'node:path'

// CSP Plugin for development vs production
const cspPlugin = () => {
  return {
    name: 'csp-plugin',
    configureServer(server: any) {
      server.middlewares.use((req: any, res: any, next: any) => {
        // Only apply CSP in development
        if (process.env.NODE_ENV !== 'production') {
          res.setHeader(
            'Content-Security-Policy',
            "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval' " +
            "http://localhost:* https://localhost:* " +
            "https://www.gstatic.com https://www.googleapis.com https://apis.google.com " +
            "https://www.googletagmanager.com https://js.stripe.com https://*.stripe.com " +
            "https://api.reeflex.ai https://browser.sentry-cdn.com https://*.sentry-cdn.com; " +
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
            "font-src 'self' https://fonts.gstatic.com; " +
            "img-src 'self' data: blob: https:; " +
            "media-src 'self' blob:; " +
            "connect-src 'self' ws://localhost:* wss://localhost:* " +
            "http://localhost:* https://localhost:* " +
            "https://*.googleapis.com https://*.firebase.googleapis.com " +
            "https://*.firebaseio.com https://*.cloudfunctions.net " +
            "https://firestore.googleapis.com https://api.stripe.com " +
            "https://connect.stripe.com https://*.stripe.com https://js.stripe.com " +
            "https://api.reeflex.ai https://sentry.io https://*.sentry.io " +
            "https://www.google-analytics.com https://www.googletagmanager.com " +
            "https://fonts.gstatic.com wss://*.firebaseio.com; " +
            "frame-src 'self' https://js.stripe.com https://checkout.stripe.com " +
            "https://connect.stripe.com https://*.stripe.com https://*.firebaseapp.com " +
            "https://*.google.com https://accounts.google.com; " +
            "object-src 'none'; " +
            "base-uri 'self'; " +
            "form-action 'self' https://checkout.stripe.com https://connect.stripe.com"
          )
        }
        next()
      })
    }
  }
}

export default defineConfig({
  plugins: [
    react(),
    cspPlugin()
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/components': resolve(__dirname, './src/components'),
      '@/pages': resolve(__dirname, './src/pages'),
      '@/hooks': resolve(__dirname, './src/hooks'),
      '@/contexts': resolve(__dirname, './src/contexts'),
      '@/utils': resolve(__dirname, './src/utils'),
      '@/firebase': resolve(__dirname, './src/firebase'),
      '@/test': resolve(__dirname, './src/test')
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          firebase: ['firebase/app', 'firebase/auth', 'firebase/firestore'],
          ui: ['lucide-react', 'react-hot-toast']
        }
      }
    },
    sourcemap: true,
    target: 'es2015',
    minify: 'esbuild',
    chunkSizeWarningLimit: 1000
  },
  server: {
    port: 5173,
    host: true,
    cors: true
  },
  preview: {
    port: 4173,
    host: true
  }
})
