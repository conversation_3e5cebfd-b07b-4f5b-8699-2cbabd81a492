// Polyfills for React Scheduler and other browser APIs
// This file should be imported before React to ensure proper initialization

// Performance API polyfill
if (typeof globalThis !== 'undefined' && !globalThis.performance) {
  globalThis.performance = {
    now: () => Date.now(),
    mark: () => {},
    measure: () => {},
    getEntriesByName: () => [],
    getEntriesByType: () => [],
    clearMarks: () => {},
    clearMeasures: () => {},
    timing: {
      navigationStart: Date.now(),
      loadEventEnd: Date.now()
    }
  } as any;
}

// Ensure window.performance exists
if (typeof window !== 'undefined' && !window.performance) {
  window.performance = globalThis.performance as any;
}

// MessageChannel polyfill for React Scheduler
if (typeof window !== 'undefined' && !window.MessageChannel) {
  class MessageChannelPolyfill {
    port1: MessagePort;
    port2: MessagePort;

    constructor() {
      const channel = this;
      
      this.port1 = {
        onmessage: null,
        postMessage: function(data: any) {
          setTimeout(() => {
            if (channel.port2.onmessage) {
              channel.port2.onmessage({ data } as MessageEvent);
            }
          }, 0);
        },
        start: () => {},
        close: () => {},
        addEventListener: () => {},
        removeEventListener: () => {},
        dispatchEvent: () => false
      } as MessagePort;

      this.port2 = {
        onmessage: null,
        postMessage: function(data: any) {
          setTimeout(() => {
            if (channel.port1.onmessage) {
              channel.port1.onmessage({ data } as MessageEvent);
            }
          }, 0);
        },
        start: () => {},
        close: () => {},
        addEventListener: () => {},
        removeEventListener: () => {},
        dispatchEvent: () => false
      } as MessagePort;
    }
  }

  window.MessageChannel = MessageChannelPolyfill as any;
}

// requestIdleCallback polyfill
if (typeof window !== 'undefined' && !window.requestIdleCallback) {
  window.requestIdleCallback = function(callback: IdleRequestCallback, options?: IdleRequestOptions) {
    const start = Date.now();
    const timeout = options?.timeout || 50;
    
    return setTimeout(() => {
      callback({
        didTimeout: false,
        timeRemaining: () => Math.max(0, timeout - (Date.now() - start))
      });
    }, 1) as any;
  };
}

// cancelIdleCallback polyfill
if (typeof window !== 'undefined' && !window.cancelIdleCallback) {
  window.cancelIdleCallback = function(id: number) {
    clearTimeout(id);
  };
}

// requestAnimationFrame polyfill (fallback)
if (typeof window !== 'undefined' && !window.requestAnimationFrame) {
  window.requestAnimationFrame = function(callback: FrameRequestCallback) {
    return setTimeout(() => callback(Date.now()), 16);
  };
}

// cancelAnimationFrame polyfill (fallback)
if (typeof window !== 'undefined' && !window.cancelAnimationFrame) {
  window.cancelAnimationFrame = function(id: number) {
    clearTimeout(id);
  };
}

// Scheduler-specific polyfills
if (typeof window !== 'undefined') {
  // Ensure scheduler can access these globals
  (window as any).unstable_now = () => performance.now();
  (window as any).unstable_scheduleCallback = (priority: any, callback: any, options?: any) => {
    return setTimeout(callback, 0);
  };
  (window as any).unstable_cancelCallback = (id: any) => {
    clearTimeout(id);
  };
}

// Console polyfill for environments that might not have it
if (typeof console === 'undefined') {
  (globalThis as any).console = {
    log: () => {},
    error: () => {},
    warn: () => {},
    info: () => {},
    debug: () => {},
    trace: () => {},
    group: () => {},
    groupEnd: () => {},
    time: () => {},
    timeEnd: () => {},
    clear: () => {},
    count: () => {},
    assert: () => {}
  };
}

console.log('✅ Polyfills loaded successfully');
