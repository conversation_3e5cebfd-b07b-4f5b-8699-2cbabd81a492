# Console Fixes Deployment Checklist

## Pre-Deployment Verification

### ✅ Code Changes Verification
- [ ] **firebase.json**: CSP headers updated with Stripe and Google Fonts domains
- [ ] **src/utils/sentry.ts**: Error changed to warning for missing VITE_SENTRY_DSN
- [ ] **src/utils/reeflex.ts**: Undefined value handling implemented with conditional spread
- [ ] **public/sw.js**: Trusted domains added and error handling improved

### ✅ Build Verification
- [ ] Run `npm run build` successfully
- [ ] No TypeScript errors
- [ ] No build warnings related to our changes
- [ ] Dist folder generated correctly

### ✅ Environment Check
- [ ] Firebase CLI installed and authenticated
- [ ] Correct Firebase project selected (`firebase use --list`)
- [ ] Firebase hosting configuration verified

## Deployment Steps

### 1. Automated Deployment (Recommended)
```powershell
# Run the deployment script
.\deploy-console-fixes.ps1
```

### 2. Manual Deployment (Alternative)
```bash
# Build the application
npm run build

# Deploy to Firebase Hosting
firebase deploy --only hosting
```

## Post-Deployment Verification

### ✅ Console Error Checks
Open your admin dashboard and verify these errors are resolved:

1. **CSP Violations Fixed**
   - [ ] No more "Refused to connect to 'https://js.stripe.com/basil/stripe.js'" errors
   - [ ] No more "Refused to connect to 'https://fonts.gstatic.com'" errors
   - [ ] Stripe.js loads successfully
   - [ ] Google Fonts load correctly

2. **Sentry Configuration Fixed**
   - [ ] No more "❌ VITE_SENTRY_DSN is required in production environment" errors
   - [ ] Only warning message appears: "⚠️ VITE_SENTRY_DSN is not configured"

3. **ReeFlex Data Validation Fixed**
   - [ ] No more "Function addDoc() called with invalid data. Unsupported field value: undefined" errors
   - [ ] ReeFlex events log successfully to Firestore

4. **Service Worker Fixed**
   - [ ] No more "Failed to fetch" errors for blocked resources
   - [ ] Service worker handles CSP violations gracefully

### ✅ Functionality Verification
- [ ] Admin dashboard loads without errors
- [ ] All admin features work correctly
- [ ] Stripe integration functions properly
- [ ] Font rendering is correct
- [ ] ReeFlex tracking works without errors

## Rollback Plan (If Issues Occur)

If any issues are discovered after deployment:

1. **Immediate Rollback**
   ```bash
   # Deploy previous version
   firebase hosting:clone SOURCE_SITE_ID:SOURCE_VERSION_ID TARGET_SITE_ID
   ```

2. **Fix and Redeploy**
   - Identify the specific issue
   - Apply targeted fix
   - Test locally
   - Redeploy with verification

## Success Criteria

✅ **Deployment is successful when:**
- Build completes without errors
- Firebase deployment succeeds
- Admin dashboard loads cleanly (no console errors)
- All admin functionality works as expected
- Performance is maintained or improved

## Monitoring

After deployment, monitor for:
- Any new console errors
- Performance impact
- User reports of issues
- Sentry error reports (if configured)

## Documentation Updates

- [ ] Update deployment logs
- [ ] Record any lessons learned
- [ ] Update team on changes deployed
