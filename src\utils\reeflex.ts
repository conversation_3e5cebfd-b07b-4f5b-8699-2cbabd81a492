import { collection, addDoc, Timestamp, writeBatch, doc } from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { auth } from '../firebase/config';
import { getDeviceInfo, getSimplifiedDeviceInfo } from './deviceInfo';
import { Sentry } from './sentry';

// ReeFlex event types
export type ReeFlexEventType = 
  | 'page_viewed'
  | 'user_interaction'
  | 'form_submitted'
  | 'api_slow'
  | 'api_failed'
  | 'api_error'
  | 'javascript_error'
  | 'promise_rejection'
  | 'slow_navigation'
  | 'slow_resource'
  | 'long_task'
  | 'checkout_started'
  | 'checkout_completed'
  | 'checkout_failed'
  | 'payment_timeout'
  | 'listing_created'
  | 'listing_viewed'
  | 'listing_updated'
  | 'listing_deleted'
  | 'feedback_given'
  | 'search_performed'
  | 'filter_applied'
  | 'user_confused'
  | 'feature_used';

// ReeFlex feedback types
export type ReeFlexFeedbackType = 'positive' | 'negative' | 'neutral';

// Interface for ReeFlex events
export interface ReeFlexEvent {
  eventType: ReeFlexEventType;
  userId?: string;
  userRole?: string;
  timestamp: Timestamp;
  route: string;
  data: Record<string, unknown>;
  deviceInfo?: Record<string, unknown>;
  sentryEventId?: string;
}

// Interface for ReeFlex feedback
export interface ReeFlexFeedback {
  feedbackType: ReeFlexFeedbackType;
  category: string;
  message: string;
  userId?: string;
  userRole?: string;
  timestamp: Timestamp;
  route: string;
  deviceInfo?: Record<string, unknown>;
  sentryEventId?: string;
}

// Collection references
const REEFLEX_ACTIVITY_COLLECTION = 'reeflex_activity';
const REEFLEX_FEEDBACK_COLLECTION = 'reeflex_feedback';

// Event batching for high-frequency events
const eventBatchQueue: ReeFlexEvent[] = [];
let batchTimer: NodeJS.Timeout | null = null;
const BATCH_INTERVAL = 10000; // 10 seconds
const BATCH_SIZE = 20; // Max events per batch

// Deduplication cache for error events
const errorDedupeCache = new Map<string, number>();
const ERROR_DEDUPE_TIMEOUT = 60000; // 1 minute

/**
 * Log a ReeFlex event to Firestore
 */
export const logReeFlexEvent = async (
  eventType: ReeFlexEventType,
  data: Record<string, unknown> = {}
): Promise<void> => {
  // In development mode, reduce noise and don't send to Firestore
  if (import.meta.env.DEV || import.meta.env.VITE_DISABLE_ANALYTICS === 'true') {
    // Only log critical events in development to reduce noise
    const criticalEvents = ['checkout_failed', 'payment_timeout', 'javascript_error', 'api_error'];
    if (criticalEvents.includes(eventType)) {
      console.log(`[ReeFlex Event] ${eventType}`, data);
    }
    return;
  }
  
  try {
    const currentUser = auth.currentUser;
    const route = window.location.pathname;
    
    // Create event object - filter out undefined values to prevent Firestore errors
    const deviceInfo = shouldIncludeDeviceInfo(eventType) ? getSimplifiedDeviceInfo() : null;
    const sentryEventId = Sentry.lastEventId();

    const event: ReeFlexEvent = {
      eventType,
      userId: currentUser?.uid || 'anonymous',
      userRole: data.userRole || 'anonymous',
      timestamp: Timestamp.now(),
      route,
      data,
      ...(deviceInfo && { deviceInfo }),
      ...(sentryEventId && { sentryEventId })
    };
    
    // Handle error deduplication
    if (isErrorEvent(eventType)) {
      const errorKey = generateErrorKey(eventType, data);
      if (isDuplicateError(errorKey)) {
        // Skip duplicate errors
        return;
      }
    }
    
    // For high-frequency events, use batching
    if (shouldBatchEvent(eventType)) {
      addToBatch(event);
    } else {
      // For important events, log immediately
      await addDoc(collection(firestore, REEFLEX_ACTIVITY_COLLECTION), event);
    }
  } catch (error) {
    console.error('Error logging ReeFlex event:', error);
    // Don't throw - we don't want to break the app if logging fails
  }
};

/**
 * Submit user feedback through ReeFlex
 */
export const submitReeFlexFeedback = async (feedback: {
  feedbackType: ReeFlexFeedbackType;
  category: string;
  message: string;
  route: string;
  sentryEventId?: string;
}): Promise<void> => {
  // In development mode, reduce noise and don't send to Firestore
  if (import.meta.env.DEV || import.meta.env.VITE_DISABLE_ANALYTICS === 'true') {
    console.log('[ReeFlex Feedback]', feedback);
    return;
  }
  
  try {
    const currentUser = auth.currentUser;
    
    // Create feedback object - filter out undefined values
    const deviceInfo = getDeviceInfo();
    const sentryEventId = Sentry.lastEventId();

    const feedbackData: ReeFlexFeedback = {
      ...feedback,
      userId: currentUser?.uid || 'anonymous',
      userRole: 'anonymous',
      timestamp: Timestamp.now(),
      ...(deviceInfo && { deviceInfo }),
      ...(sentryEventId && { sentryEventId })
    };
    
    // Log to Firestore
    await addDoc(collection(firestore, REEFLEX_FEEDBACK_COLLECTION), feedbackData);
    
    // Also log as an event
    await logReeFlexEvent('feedback_given', {
      feedback_type: feedback.feedbackType,
      category: feedback.category,
      has_message: !!feedback.message,
      route: feedback.route
    });
  } catch (error) {
    console.error('Error submitting ReeFlex feedback:', error);
    throw error; // Rethrow for UI handling
  }
};

/**
 * Add event to batch queue
 */
const addToBatch = (event: ReeFlexEvent): void => {
  eventBatchQueue.push(event);
  
  // Start batch timer if not already running
  if (!batchTimer && eventBatchQueue.length === 1) {
    batchTimer = setTimeout(processBatch, BATCH_INTERVAL);
  }
  
  // Process immediately if queue is full
  if (eventBatchQueue.length >= BATCH_SIZE) {
    if (batchTimer) {
      clearTimeout(batchTimer);
      batchTimer = null;
    }
    processBatch();
  }
};

/**
 * Process batch of events
 */
const processBatch = async (): Promise<void> => {
  if (eventBatchQueue.length === 0) return;
  
  const eventsToProcess = [...eventBatchQueue];
  eventBatchQueue.length = 0; // Clear the queue
  
  // In development mode, reduce noise and don't send to Firestore
  if (import.meta.env.DEV || import.meta.env.VITE_DISABLE_ANALYTICS === 'true') {
    // Only log if there are critical events in the batch
    const criticalEventCount = eventsToProcess.filter(e => isCriticalEvent(e.eventType)).length;
    if (criticalEventCount > 0) {
      console.log(`[ReeFlex Batch] Processing ${eventsToProcess.length} events (${criticalEventCount} critical)`);
    }
    batchTimer = null;
    
    // If new events came in while processing, start a new timer
    if (eventBatchQueue.length > 0) {
      batchTimer = setTimeout(processBatch, BATCH_INTERVAL);
    }
    return;
  }
  
  try {
    const batch = writeBatch(firestore);
    
    eventsToProcess.forEach(event => {
      const docRef = doc(collection(firestore, REEFLEX_ACTIVITY_COLLECTION));
      batch.set(docRef, event);
    });
    
    await batch.commit();
  } catch (error) {
    console.error('Error processing ReeFlex event batch:', error);
    
    // If batch fails, try to save critical events individually
    eventsToProcess.forEach(async event => {
      if (isCriticalEvent(event.eventType)) {
        try {
          await addDoc(collection(firestore, REEFLEX_ACTIVITY_COLLECTION), event);
        } catch (innerError) {
          console.error('Failed to save critical event:', innerError);
        }
      }
    });
  } finally {
    batchTimer = null;
    
    // If new events came in while processing, start a new timer
    if (eventBatchQueue.length > 0) {
      batchTimer = setTimeout(processBatch, BATCH_INTERVAL);
    }
  }
};

/**
 * Determine if an event should be batched
 */
const shouldBatchEvent = (eventType: ReeFlexEventType): boolean => {
  const highFrequencyEvents: ReeFlexEventType[] = [
    'page_viewed',
    'user_interaction',
    'listing_viewed'
  ];
  
  return highFrequencyEvents.includes(eventType);
};

/**
 * Determine if an event is critical and should be saved individually if batch fails
 */
const isCriticalEvent = (eventType: ReeFlexEventType): boolean => {
  const criticalEvents: ReeFlexEventType[] = [
    'javascript_error',
    'api_error',
    'checkout_failed',
    'payment_timeout',
    'api_failed'
  ];
  
  return criticalEvents.includes(eventType);
};

/**
 * Determine if device info should be included with this event type
 */
const shouldIncludeDeviceInfo = (eventType: ReeFlexEventType): boolean => {
  const eventsWithDeviceInfo: ReeFlexEventType[] = [
    'javascript_error',
    'api_error',
    'slow_navigation',
    'slow_resource',
    'long_task',
    'checkout_failed',
    'payment_timeout',
    'user_confused'
  ];
  
  return eventsWithDeviceInfo.includes(eventType);
};

/**
 * Check if an event is an error event
 */
const isErrorEvent = (eventType: ReeFlexEventType): boolean => {
  const errorEvents: ReeFlexEventType[] = [
    'javascript_error',
    'promise_rejection',
    'api_error',
    'api_failed'
  ];
  
  return errorEvents.includes(eventType);
};

/**
 * Generate a deduplication key for error events
 */
const generateErrorKey = (eventType: ReeFlexEventType, data: Record<string, unknown>): string => {
  let key = eventType;
  
  if (eventType === 'javascript_error' || eventType === 'promise_rejection') {
    key += `:${data.message || ''}`;
  } else if (eventType === 'api_error' || eventType === 'api_failed') {
    key += `:${data.url || ''}:${data.status || ''}`;
  }
  
  return key;
};

/**
 * Check if an error is a duplicate within the deduplication window
 */
const isDuplicateError = (errorKey: string): boolean => {
  const now = Date.now();
  
  // Clean up expired entries
  for (const [key, timestamp] of errorDedupeCache.entries()) {
    if (now - timestamp > ERROR_DEDUPE_TIMEOUT) {
      errorDedupeCache.delete(key);
    }
  }
  
  // Check if this error is a duplicate
  if (errorDedupeCache.has(errorKey)) {
    return true;
  }
  
  // Add to cache
  errorDedupeCache.set(errorKey, now);
  return false;
};

/**
 * Flush any pending events
 * Call this when the app is about to unload
 */
export const flushReeFlexEvents = (): void => {
  if (eventBatchQueue.length > 0) {
    if (batchTimer) {
      clearTimeout(batchTimer);
      batchTimer = null;
    }
    processBatch();
  }
};

// Flush events when page becomes hidden (modern alternative to beforeunload)
if (typeof window !== 'undefined') {
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      flushReeFlexEvents();
    }
  });
}