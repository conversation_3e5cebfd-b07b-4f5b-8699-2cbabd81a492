// This service worker is manually created and will be used instead of the one generated by Workbox
// during the build process. It provides offline functionality and caching strategies.

// PWA manifest injection point - will be replaced by vite-plugin-pwa during build
// Workbox will replace this with the actual manifest during build
self.__WB_MANIFEST;

// Cache names
const STATIC_CACHE = 'hive-campus-static-v1';
const DYNAMIC_CACHE = 'hive-campus-dynamic-v1';
const IMAGE_CACHE = 'hive-campus-images-v1';
const FONT_CACHE = 'hive-campus-fonts-v1';
const API_CACHE = 'hive-campus-api-v1';

// Resources to cache immediately on install
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/robots.txt'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('[Service Worker] Precaching App Shell');
        // Cache static assets individually to avoid single failure breaking everything
        return Promise.allSettled(
          STATIC_ASSETS.map(asset => 
            cache.add(asset).catch(err => 
              console.warn(`[Service Worker] Failed to cache ${asset}:`, err.message)
            )
          )
        );
      })
      .then(() => {
        console.log('[Service Worker] Skip waiting on install');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[Service Worker] Install failed:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating Service Worker...');
  
  const currentCaches = [STATIC_CACHE, DYNAMIC_CACHE, IMAGE_CACHE, FONT_CACHE, API_CACHE];
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return cacheNames.filter(cacheName => !currentCaches.includes(cacheName));
      })
      .then(cachesToDelete => {
        return Promise.all(cachesToDelete.map(cacheToDelete => {
          console.log('[Service Worker] Deleting old cache:', cacheToDelete);
          return caches.delete(cacheToDelete);
        }));
      })
      .then(() => {
        console.log('[Service Worker] Claiming clients');
        return self.clients.claim();
      })
  );
});

// Helper function to determine if a request is for an image
const isImageRequest = (request) => {
  return request.destination === 'image' || 
         request.url.match(/\.(jpe?g|png|gif|svg|webp)$/i);
};

// Helper function to determine if a request is for a font
const isFontRequest = (request) => {
  return request.destination === 'font' || 
         request.url.match(/\.(woff2?|eot|ttf|otf)$/i) ||
         request.url.includes('fonts.googleapis.com') ||
         request.url.includes('fonts.gstatic.com');
};

// Helper function to determine if a request is for an API
const isApiRequest = (request) => {
  return request.url.includes('firestore.googleapis.com') || 
         request.url.includes('api.stripe.com') ||
         request.url.includes('firebase');
};

// Helper function to determine if a request is for HTML navigation
const isNavigationRequest = (request) => {
  return request.mode === 'navigate';
};

// Fetch event - handle different caching strategies based on request type
self.addEventListener('fetch', (event) => {
  const request = event.request;
  const url = new URL(request.url);
  
  // Skip development server requests (WebSocket, HMR, etc.)
  if (url.hostname === 'localhost' && 
      (url.pathname.includes('@vite') || 
       url.pathname.includes('__vite') ||
       url.protocol === 'ws:' ||
       url.protocol === 'wss:')) {
    return;
  }
  
  // Don't cache requests to other origins except trusted ones
  if (url.origin !== self.location.origin && 
      !url.hostname.includes('googleapis.com') && 
      !url.hostname.includes('gstatic.com') &&
      !url.hostname.includes('stripe.com') &&
      !url.hostname.includes('firebaseapp.com')) {
    return;
  }
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests with appropriate strategies
  if (isImageRequest(request)) {
    // Cache images with a stale-while-revalidate strategy
    event.respondWith(staleWhileRevalidate(request, IMAGE_CACHE));
  } else if (isFontRequest(request)) {
    // Cache fonts with a cache-first strategy (they rarely change)
    event.respondWith(cacheFirst(request, FONT_CACHE));
  } else if (isApiRequest(request)) {
    // Handle API requests with a network-first strategy
    event.respondWith(networkFirst(request, API_CACHE));
  } else if (isNavigationRequest(request)) {
    // Handle navigation requests with a network-first strategy
    event.respondWith(networkFirst(request, STATIC_CACHE));
  } else {
    // For all other requests, use a stale-while-revalidate strategy
    event.respondWith(staleWhileRevalidate(request, DYNAMIC_CACHE));
  }
});

// Network First strategy - try network, fall back to cache
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    // Cache the response for future use
    const cache = await caches.open(cacheName);
    cache.put(request, networkResponse.clone());
    
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    throw error;
  }
}

// Cache First strategy - try cache, fall back to network
async function cacheFirst(request, cacheName) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    // Cache the response for future use
    const cache = await caches.open(cacheName);
    cache.put(request, networkResponse.clone());
    
    return networkResponse;
  } catch (error) {
    throw error;
  }
}

// Stale While Revalidate strategy - return cache immediately, but update cache in background
async function staleWhileRevalidate(request, cacheName) {
  const cachedResponse = await caches.match(request);
  
  const fetchPromise = fetch(request)
    .then(networkResponse => {
      // Clone the response immediately before using it
      const responseToCache = networkResponse.clone();
      
      // Use async/await pattern with Promise.resolve to handle the cache operation properly
      caches.open(cacheName)
        .then(cache => cache.put(request, responseToCache))
        .catch(err => {
          // Only log cache errors in development or for critical errors
          if (err.name !== 'QuotaExceededError') {
            console.warn('Cache put error:', err.message);
          }
        });
        
      return networkResponse;
    })
    .catch(error => {
      // Only log fetch errors that aren't network connectivity issues
      if (!error.message.includes('Failed to fetch') && !error.message.includes('NetworkError')) {
        console.warn('Fetch error:', error.message);
      }
      // If the fetch fails, just return null so we can fall back to cache
      return null;
    });
  
  // Return the cached response immediately if we have one
  if (cachedResponse) {
    // Update the cache in the background
    fetchPromise;
    return cachedResponse;
  }
  
  // If we don't have a cached response, wait for the network response
  const networkResponse = await fetchPromise;
  if (networkResponse) {
    return networkResponse;
  }
  
  // If both cache and network fail, return a simple error response
  return new Response('Network error happened', {
    status: 408,
    headers: { 'Content-Type': 'text/plain' },
  });
}

// Listen for messages from the client
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});