# Deploy Console Error Fixes to Production
# This script deploys all the fixes for admin dashboard console errors

Write-Host "🚀 Deploying Console Error Fixes to Production" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "firebase.json")) {
    Write-Host "❌ Error: firebase.json not found. Please run this script from the project root." -ForegroundColor Red
    exit 1
}

# Step 1: Verify all fixes are in place
Write-Host "`n📋 Step 1: Verifying fixes are in place..." -ForegroundColor Blue

$fixesVerified = $true

# Check firebase.json CSP updates
Write-Host "  Checking firebase.json CSP configuration..." -ForegroundColor Yellow
$firebaseConfig = Get-Content "firebase.json" -Raw
if ($firebaseConfig -match "https://fonts.gstatic.com" -and $firebaseConfig -match "https://js.stripe.com") {
    Write-Host "  ✅ CSP configuration updated" -ForegroundColor Green
} else {
    Write-Host "  ❌ CSP configuration missing updates" -ForegroundColor Red
    $fixesVerified = $false
}

# Check sentry.ts fixes
Write-Host "  Checking Sentry configuration..." -ForegroundColor Yellow
if (Test-Path "src/utils/sentry.ts") {
    $sentryContent = Get-Content "src/utils/sentry.ts" -Raw
    if ($sentryContent -match "console.warn.*VITE_SENTRY_DSN.*not configured") {
        Write-Host "  ✅ Sentry graceful degradation implemented" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Sentry fixes not found" -ForegroundColor Red
        $fixesVerified = $false
    }
} else {
    Write-Host "  ❌ sentry.ts file not found" -ForegroundColor Red
    $fixesVerified = $false
}

# Check reeflex.ts fixes
Write-Host "  Checking ReeFlex configuration..." -ForegroundColor Yellow
if (Test-Path "src/utils/reeflex.ts") {
    $reeflexContent = Get-Content "src/utils/reeflex.ts" -Raw
    if ($reeflexContent -match "deviceInfo && \{ deviceInfo \}") {
        Write-Host "  ✅ ReeFlex undefined value handling implemented" -ForegroundColor Green
    } else {
        Write-Host "  ❌ ReeFlex fixes not found" -ForegroundColor Red
        $fixesVerified = $false
    }
} else {
    Write-Host "  ❌ reeflex.ts file not found" -ForegroundColor Red
    $fixesVerified = $false
}

# Check service worker fixes
Write-Host "  Checking Service Worker configuration..." -ForegroundColor Yellow
if (Test-Path "public/sw.js") {
    $swContent = Get-Content "public/sw.js" -Raw
    if ($swContent -match "fonts.gstatic.com" -and $swContent -match "js.stripe.com") {
        Write-Host "  ✅ Service Worker trusted domains updated" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Service Worker fixes not found" -ForegroundColor Red
        $fixesVerified = $false
    }
} else {
    Write-Host "  ❌ sw.js file not found" -ForegroundColor Red
    $fixesVerified = $false
}

if (-not $fixesVerified) {
    Write-Host "`n❌ Some fixes are missing. Please ensure all fixes are applied before deploying." -ForegroundColor Red
    exit 1
}

Write-Host "`n✅ All fixes verified successfully!" -ForegroundColor Green

# Step 2: Build the application
Write-Host "`n🔨 Step 2: Building application..." -ForegroundColor Blue
try {
    npm run build
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "✅ Build completed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Build failed. Please fix build errors before deploying." -ForegroundColor Red
    exit 1
}

# Step 3: Deploy to Firebase Hosting
Write-Host "`n🚀 Step 3: Deploying to Firebase Hosting..." -ForegroundColor Blue
try {
    firebase deploy --only hosting
    if ($LASTEXITCODE -ne 0) {
        throw "Deployment failed"
    }
    Write-Host "✅ Deployment completed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Deployment failed. Please check Firebase configuration." -ForegroundColor Red
    exit 1
}

# Step 4: Verify deployment
Write-Host "`n🔍 Step 4: Deployment verification..." -ForegroundColor Blue
Write-Host "Please verify the following in your browser:" -ForegroundColor Yellow
Write-Host "  1. Open browser developer console" -ForegroundColor White
Write-Host "  2. Navigate to your admin dashboard" -ForegroundColor White
Write-Host "  3. Check that these errors are gone:" -ForegroundColor White
Write-Host "     - CSP violations for Stripe.js and Google Fonts" -ForegroundColor White
Write-Host "     - Sentry DSN missing errors" -ForegroundColor White
Write-Host "     - ReeFlex undefined value errors" -ForegroundColor White
Write-Host "     - Service Worker fetch errors" -ForegroundColor White

Write-Host "`n🎉 Console Error Fixes Deployment Complete!" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green
Write-Host "Summary of deployed fixes:" -ForegroundColor Cyan
Write-Host "  ✅ Updated CSP headers in firebase.json" -ForegroundColor White
Write-Host "  ✅ Fixed Sentry DSN error handling" -ForegroundColor White
Write-Host "  ✅ Fixed ReeFlex undefined value issues" -ForegroundColor White
Write-Host "  ✅ Improved Service Worker error handling" -ForegroundColor White
Write-Host "`nYour admin dashboard should now load without console errors!" -ForegroundColor Green
